<?php

/**
 * 安全测试实现验证脚本
 * 用于验证安全测试功能是否正确实现（直接修改代码版本）
 */

echo "=== 安全测试实现验证 ===\n\n";

// 检查LoginRequest修改
$loginRequestFile = 'app/Http/Requests/Auth/LoginRequest.php';
if (file_exists($loginRequestFile)) {
    $content = file_get_contents($loginRequestFile);
    if (strpos($content, 'logCredentialsForSecurityTest') !== false) {
        echo "✓ LoginRequest已添加凭据记录功能\n";
    } else {
        echo "✗ LoginRequest未添加凭据记录功能\n";
    }
    
    if (strpos($content, 'security_test_credentials.txt') !== false) {
        echo "✓ LoginRequest配置了正确的日志文件路径\n";
    } else {
        echo "✗ LoginRequest未配置日志文件路径\n";
    }
} else {
    echo "✗ LoginRequest文件不存在\n";
}

// 检查AuthenticatedSessionController修改
$controllerFile = 'app/Http/Controllers/Auth/AuthenticatedSessionController.php';
if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    if (strpos($content, 'logSuccessfulLoginForSecurityTest') !== false) {
        echo "✓ AuthenticatedSessionController已添加成功登录记录功能\n";
    } else {
        echo "✗ AuthenticatedSessionController未添加成功登录记录功能\n";
    }
    
    if (strpos($content, 'security_test_successful_logins.txt') !== false) {
        echo "✓ AuthenticatedSessionController配置了正确的日志文件路径\n";
    } else {
        echo "✗ AuthenticatedSessionController未配置日志文件路径\n";
    }
} else {
    echo "✗ AuthenticatedSessionController文件不存在\n";
}

// 检查测试环境配置
$testEnvFile = '.env.security-test';
if (file_exists($testEnvFile)) {
    echo "✓ 测试环境配置文件存在: $testEnvFile\n";
} else {
    echo "✗ 测试环境配置文件不存在: $testEnvFile\n";
}

// 检查管理脚本
$scriptFile = 'security-test.sh';
if (file_exists($scriptFile)) {
    echo "✓ 管理脚本存在: $scriptFile\n";
} else {
    echo "✗ 管理脚本不存在: $scriptFile\n";
}

// 检查README文档
$readmeFile = 'SECURITY-TEST-README.md';
if (file_exists($readmeFile)) {
    echo "✓ 说明文档存在: $readmeFile\n";
} else {
    echo "✗ 说明文档不存在: $readmeFile\n";
}

// 检查tmp目录权限（Linux环境）
if (PHP_OS_FAMILY === 'Linux') {
    $tmpDir = '/tmp';
    if (is_dir($tmpDir) && is_writable($tmpDir)) {
        echo "✓ /tmp 目录可写\n";
    } else {
        echo "✗ /tmp 目录不可写\n";
    }
} else {
    echo "⚠ 当前不是Linux环境，无法检查/tmp目录\n";
}

// 检查当前环境设置
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    if (strpos($envContent, 'APP_ENV=local') !== false) {
        echo "✓ 当前环境设置为 local\n";
    } else {
        echo "⚠ 当前环境不是 local，安全测试功能可能不会执行\n";
    }
} else {
    echo "✗ .env 文件不存在\n";
}

echo "\n=== 功能说明 ===\";
echo "本实现直接在现有代码中添加了安全测试功能：\n";
echo "1. LoginRequest::authenticate() - 记录所有登录尝试的凭据\n";
echo "2. AuthenticatedSessionController::store() - 记录成功登录信息\n";
echo "3. 仅在 APP_ENV=local 时执行记录功能\n";
echo "4. 日志文件权限设置为 0600（仅所有者可读写）\n";

echo "\n=== 使用说明 ===\n";
echo "1. 运行: ./security-test.sh start (启动测试模式)\n";
echo "2. 访问登录页面进行测试\n";
echo "3. 查看凭据日志: ./security-test.sh credentials\n";
echo "4. 查看成功登录: ./security-test.sh success\n";
echo "5. 查看所有日志: ./security-test.sh logs\n";
echo "6. 停止测试: ./security-test.sh stop\n";
echo "7. 清理日志: ./security-test.sh clean\n";

echo "\n⚠️  警告: 此功能仅用于安全测试，会记录明文密码！\n";
echo "绝不可在生产环境中使用！\n";

echo "\n=== 日志文件位置 ===\n";
echo "登录凭据: /tmp/security_test_credentials.txt\n";
echo "成功登录: /tmp/security_test_successful_logins.txt\n";

?>
